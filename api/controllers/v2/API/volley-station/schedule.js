module.exports = {
    friendlyName: 'Get event schedule for VolleyStation',
    description: 'Returns paginated schedule data for a specific event',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event'
        },
        page: {
            type: 'number',
            defaultsTo: 1,
            description: 'Page number for pagination'
        },
        limit: {
            type: 'number',
            defaultsTo: 50,
            description: 'Items per page (max 100)'
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        try {
            // Validate pagination parameters
            const page = Math.max(1, parseInt(inputs.page) || 1);
            const limit = Math.min(100, Math.max(1, parseInt(inputs.limit) || 50));
            const offset = (page - 1) * limit;

            // Get schedule data using VolleyStation service
            const { data, totalCount } = await VolleyStationService.getSchedule(inputs.eventId, limit, offset);

            const response = createPageResponse(data, page, limit, totalCount);

            return exits.success(response);

        } catch (err) {
            sails.log.error('VolleyStation schedule error:', err);
            throw { message: 'Server Internal Error' };
        }
    }
};

function createPageResponse(data, page, limit, totalCount) {
    const totalPages = Math.ceil(totalCount / limit);
    const hasNext = page * limit < totalCount;
    const hasPrev = page > 1;
    return {
        data,
        pagination: {
            page,
            limit,
            total: totalCount,
            totalPages,
            hasNext,
            hasPrev
        }
    }
}
