const PaginationUtils = require('../../../lib/PaginationUtils');

module.exports = {
    friendlyName: 'Get event schedule for VolleyStation',
    description: 'Returns paginated schedule data for a specific event',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event'
        },
        page: {
            type: 'number',
            defaultsTo: 1,
            description: 'Page number for pagination'
        },
        pageSize: {
            type: 'number',
            defaultsTo: 50,
            description: 'Items per page (max 100)'
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        try {
            // Validate pagination parameters
            const { page, pageSize, offset } = PaginationUtils.validatePaginationParams(
                inputs.page,
                inputs.pageSize
            );

            // Get schedule data using VolleyStation service
            const { data, totalCount } = await VolleyStationService.getSchedule(inputs.eventId, pageSize, offset);

            // Create standardized pagination response
            const response = PaginationUtils.createPaginationResponse(data, page, pageSize, totalCount);

            return exits.success(response);

        } catch (err) {
            sails.log.error('VolleyStation schedule error:', err);
            throw { message: 'Server Internal Error' };
        }
    }
};
