module.exports = {
    friendlyName: 'Get team roster data for VolleyStation',
    description: 'Returns paginated team roster data including athletes and staff for a specific event',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event'
        },
        page: {
            type: 'number',
            defaultsTo: 1,
            description: 'Page number for pagination'
        },
        limit: {
            type: 'number',
            defaultsTo: 50,
            description: 'Items per page (max 100)'
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        try {
            // Validate pagination parameters
            const page = Math.max(1, parseInt(inputs.page) || 1);
            const limit = Math.min(100, Math.max(1, parseInt(inputs.limit) || 50));
            const offset = (page - 1) * limit;

            // Get team roster data using VolleyStation service
            const rosterData = await VolleyStationService.getTeamRoster(inputs.eventId, limit, offset);
            const totalCount = await VolleyStationService.getTeamRosterCount(inputs.eventId);

            const response = VolleyStationService.createPaginationResponse(rosterData, page, limit, totalCount);

            return exits.success(response);

        } catch (err) {
            sails.log.error('VolleyStation team roster error:', err);
            throw { message: 'Server Internal Error' };
        }
    }
};
