const PaginationUtils = require('../../../lib/PaginationUtils');

module.exports = {
    friendlyName: 'Get team roster data for VolleyStation',
    description: 'Returns paginated team roster data including athletes and staff for a specific event',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event'
        },
        page: {
            type: 'number',
            defaultsTo: 1,
            description: 'Page number for pagination'
        },
        pageSize: {
            type: 'number',
            defaultsTo: 50,
            description: 'Items per page (max 100)'
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        try {
            // Validate pagination parameters
            const { page, pageSize, offset } = PaginationUtils.validatePaginationParams(
                inputs.page,
                inputs.pageSize
            );

            // Get team roster data using VolleyStation service
            const rosterData = await VolleyStationService.getTeamRoster(inputs.eventId, pageSize, offset);
            const totalCount = await VolleyStationService.getTeamRosterCount(inputs.eventId);

            // Create standardized pagination response
            const response = PaginationUtils.createPaginationResponse(rosterData, page, pageSize, totalCount);

            return exits.success(response);

        } catch (err) {
            sails.log.error('VolleyStation team roster error:', err);
            throw { message: 'Server Internal Error' };
        }
    }
};
