/**
 * Pagination utility functions for standardizing pagination across controllers
 */
class PaginationUtils {
    /**
     * Validate and normalize pagination parameters
     * @param {number} page - Page number (1-based)
     * @param {number} pageSize - Items per page
     * @param {number} maxPageSize - Maximum allowed page size (default: 100)
     * @returns {Object} Normalized pagination parameters
     */
    static validatePaginationParams(page, pageSize, maxPageSize = 100) {
        const normalizedPage = Math.max(1, parseInt(page) || 1);
        const normalizedPageSize = Math.min(maxPageSize, Math.max(1, parseInt(pageSize) || 50));
        const offset = (normalizedPage - 1) * normalizedPageSize;

        return {
            page: normalizedPage,
            pageSize: normalizedPageSize,
            offset
        };
    }

    /**
     * Create a standardized pagination response
     * @param {Array} data - The data array
     * @param {number} page - Current page number
     * @param {number} pageSize - Items per page
     * @param {number} totalCount - Total number of items
     * @returns {Object} Standardized pagination response
     */
    static createPaginationResponse(data, page, pageSize, totalCount) {
        const totalPages = Math.ceil(totalCount / pageSize);
        const hasNext = page * pageSize < totalCount;
        const hasPrev = page > 1;

        return {
            data,
            pagination: {
                page,
                pageSize,
                total: totalCount,
                totalPages,
                hasNext,
                hasPrev
            }
        };
    }

    /**
     * Get paginated data from VolleyStation service and return formatted response
     * @param {Function} serviceMethod - The service method to call
     * @param {Array} serviceArgs - Arguments to pass to the service method
     * @param {number} page - Page number
     * @param {number} pageSize - Items per page
     * @param {number} totalCount - Total count (if already known)
     * @returns {Object} Formatted pagination response
     */
    static async getPaginatedResponse(serviceMethod, serviceArgs, page, pageSize, totalCount = null) {
        const { page: normalizedPage, pageSize: normalizedPageSize, offset } = 
            this.validatePaginationParams(page, pageSize);

        // Call the service method with normalized parameters
        const data = await serviceMethod(...serviceArgs, normalizedPageSize, offset);

        return this.createPaginationResponse(data, normalizedPage, normalizedPageSize, totalCount);
    }
}

module.exports = PaginationUtils;
