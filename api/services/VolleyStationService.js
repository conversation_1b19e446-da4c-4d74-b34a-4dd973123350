class VolleyStationService {
    constructor() {}

    /**
     * Create a pagination response object
     * @param {Array} data - The data array
     * @param {number} page - Current page number
     * @param {number} limit - Items per page
     * @param {number} totalCount - Total number of items
     * @returns {Object} Pagination response
     */
    createPaginationResponse(data, page, limit, totalCount) {
        const totalPages = Math.ceil(totalCount / limit);
        const hasNext = page * limit < totalCount;
        const hasPrev = page > 1;
        return {
            data,
            pagination: {
                page,
                limit,
                total: totalCount,
                totalPages,
                hasNext,
                hasPrev
            }
        };
    }

    /**
     * Get schedule data for a specific event with pagination
     * @param {number} eventId - The event ID
     * @param {number} limit - Items per page
     * @param {number} offset - Offset for pagination
     * @returns {Promise<Object>} Schedule data and total count
     */
    async getSchedule(eventId, limit, offset) {
        const query = squel
            .select()
            .from('event', 'e')
            .field('d.gender')
            .field('d.name', 'division_name')
            .field('d.division_id')
            .field('m.match_id', 'match_uuid')
            .field('m.event_id', 'event')
            .field(`m.event_id || '_' || m.division_short_name || '_' || m.display_name`, 'match_id')
            .field('m.division_short_name', 'div')
            .field('m.day')
            .field(`to_char(m.secs_start AT TIME ZONE e.timezone, 'YYYY-MM-DD"T"HH24:MI:SS"Z"')`, 'date_time')
            .field('c.sort_priority', 'court')
            .field('c.short_name', 'court_alpha')
            .field('pb.display_name', 'pool')
            .field('m.team1_roster_id')
            .field('m.team2_roster_id')
            .field('m.ref_roster_id')
            .field('t1.team_name', 'team_1_name')
            .field('t2.team_name', 'team_2_name')
            .field('tr.team_name', 'ref_name')
            .field('t1.master_team_id', 'master_team_id_1')
            .field('t2.master_team_id', 'master_team_id_2')
            .field('m.type', 'match_type')
            .field(`(SELECT jsonb_object_agg(key, value) FROM jsonb_each_text(m.results) WHERE key LIKE 'set%')`, 'results')
            .field(`u.first || ' ' || u.last`, 'official')
            .field('COUNT(*) OVER()', 'total_count')
            .join('division', 'd', 'd.event_id = e.event_id')
            .join('matches', 'm', squel.expr()
                .and('m.event_id = d.event_id')
                .and('m.division_id = d.division_id'))
            .join('poolbrackets', 'pb', 'pb.uuid = m.pool_bracket_id')
            .join('courts', 'c', 'c.uuid = m.court_id')
            .left_join('roster_team', 't1', squel.expr()
                .and('t1.roster_team_id = m.team1_roster_id')
                .and('t1.event_id = d.event_id')
                .and('t1.division_id = d.division_id')
                .and('t1.deleted IS NULL')
                .and('t1.status_entry = 12'))
            .left_join('roster_team', 't2', squel.expr()
                .and('t2.roster_team_id = m.team2_roster_id')
                .and('t2.event_id = d.event_id')
                .and('t2.division_id = d.division_id')
                .and('t2.deleted IS NULL')
                .and('t2.status_entry = 12'))
            .left_join('roster_team', 'tr', squel.expr()
                .and('tr.roster_team_id = m.ref_roster_id')
                .and('tr.event_id = d.event_id')
                .and('tr.division_id = d.division_id')
                .and('tr.deleted IS NULL')
                .and('tr.status_entry = 12'))
            .left_join(
                squel.rstr(
                    `LATERAL (
                        SELECT eos1.event_official_id
                        FROM event_official_schedule eos1
                        WHERE eos1.event_id = m.event_id
                            AND eos1.division_id = m.division_id
                            AND eos1.match_name = m.display_name
                            AND eos1.ref_num = 1
                        ORDER BY eos1.event_official_schedule_id DESC
                        LIMIT 1
                     )`
                ),
                'eos',
                'TRUE'
            )
            .left_join('event_official', 'eof', 'eof.event_official_id = eos.event_official_id')
            .left_join('official', 'of', 'of.official_id = eof.official_id')
            .left_join(squel.rstr('"user"'), 'u', 'u.user_id = of.user_id')
            .where('e.event_id = ?', eventId)
            .where('m.team1_roster_id IS NOT NULL OR m.team2_roster_id IS NOT NULL')
            .order('m.secs_start')
            .limit(limit)
            .offset(offset);

        const { rows } = await Db.query(query);
        let totalCount = 0;
        rows.forEach(row => {
            if (!totalCount) {
                totalCount = parseInt(row.total_count);
            }
            delete row.total_count;
        });

        return { data: rows, totalCount };
    }

    /**
     * Get team roster data for a specific event with pagination
     * @param {number} eventId - The event ID
     * @param {number} limit - Items per page
     * @param {number} offset - Offset for pagination
     * @returns {Promise<Array>} Team roster data
     */
    async getTeamRoster(eventId, limit, offset) {
        // Get teams with their basic information
        const teamsQuery = squel
            .select()
            .from('roster_team', 'rt')
            .field('rt.roster_team_id', 'team_id')
            .field('rt.team_name')
            .field('mc.club_name')
            .field('d.name', 'division')
            .field('pb.display_name', 'pool')
            .field('rt.seed')
            .field('rt.status_entry')
            .field(`CASE
                WHEN rt.status_entry = 12 THEN 'confirmed'
                WHEN rt.status_entry = 25 THEN 'not_paid'
                WHEN rt.status_entry = 23 THEN 'partial_paid'
                ELSE 'pending'
            END`, 'status')
            .field(`to_char(rt.created, 'YYYY-MM-DD')`, 'registration_date')
            .join('roster_club', 'rc', 'rc.roster_club_id = rt.roster_club_id')
            .join('master_club', 'mc', 'mc.master_club_id = rc.master_club_id')
            .join('event', 'e', 'e.event_id = rc.event_id')
            .left_join('division', 'd', 'd.division_id = rt.division_id')
            .left_join('poolbrackets', 'pb', 'pb.uuid = rt.poolbracket_id')
            .where('e.event_id = ?', eventId)
            // .where('e.enable_volley_station = true')
            .where('rt.deleted IS NULL')
            .order('rt.team_name')
            .limit(limit)
            .offset(offset);

        const { rows: teams } = await Db.query(teamsQuery);

        // For each team, get athletes and staff
        const teamsWithRoster = await Promise.all(teams.map(async (team) => {
            const [athletes, staff] = await Promise.all([
                this._getTeamAthletes(team.team_id),
                this._getTeamStaff(team.team_id)
            ]);

            return {
                ...team,
                athletes,
                staff,
                team_stats: {
                    total_athletes: athletes.length,
                    total_staff: staff.length,
                    registration_date: team.registration_date
                }
            };
        }));

        return teamsWithRoster;
    }

    /**
     * Get total count of team roster items for pagination
     * @param {number} eventId - The event ID
     * @returns {Promise<number>} Total count
     */
    async getTeamRosterCount(eventId) {
        const query = squel
            .select()
            .from('roster_team', 'rt')
            .field('COUNT(*)', 'total')
            .join('roster_club', 'rc', 'rc.roster_club_id = rt.roster_club_id')
            .join('event', 'e', 'e.event_id = rc.event_id')
            .where('e.event_id = ?', eventId)
            // .where('e.enable_volley_station = true')
            .where('rt.deleted IS NULL');

        const { rows } = await Db.query(query);
        return parseInt(rows[0]?.total || 0);
    }

    /**
     * Get athletes for a specific team
     * @param {number} rosterTeamId - The roster team ID
     * @returns {Promise<Array>} Athletes data
     * @private
     */
    async _getTeamAthletes(rosterTeamId) {
        const query = squel
            .select()
            .from('roster_athlete', 'ra')
            .field('ma.master_athlete_id', 'athlete_id')
            .field('INITCAP(ma.first)', 'first_name')
            .field('INITCAP(ma.last)', 'last_name')
            .field('ma.jersey', 'jersey_number')
            .field('sp.name', 'position')
            .field('ma.height')
            .field('ma.age')
            .field('ma.gradyear', 'year')
            .field('CONCAT(ma.city, \', \', ma.state)', 'hometown')
            .field('ma.gender')
            .join('master_athlete', 'ma', 'ma.master_athlete_id = ra.master_athlete_id')
            .left_join('sport_position', 'sp', 'sp.sport_position_id = ma.sport_position_id')
            .where('ra.roster_team_id = ?', rosterTeamId)
            .where('ra.deleted IS NULL')
            .order('ma.jersey ASC, ma.last ASC, ma.first ASC');

        const { rows } = await Db.query(query);
        return rows;
    }

    /**
     * Get staff for a specific team
     * @param {number} rosterTeamId - The roster team ID
     * @returns {Promise<Array>} Staff data
     * @private
     */
    async _getTeamStaff(rosterTeamId) {
        const query = squel
            .select()
            .from('roster_staff', 'rs')
            .field('ms.master_staff_id', 'staff_id')
            .field('INITCAP(ms.first)', 'first_name')
            .field('INITCAP(ms.last)', 'last_name')
            .field('COALESCE(rr.short_name, \'Staff\')', 'role')
            .field('ms.email')
            .field('ms.phone')
            .field('ms.cert')
            .field('ms.gender')
            .join('master_staff', 'ms', 'ms.master_staff_id = rs.master_staff_id')
            .left_join('roster_role', 'rr', 'rr.role_id = rs.role_id')
            .where('rs.roster_team_id = ?', rosterTeamId)
            .where('rs.deleted IS NULL')
            .order('rs.primary DESC, ms.last ASC, ms.first ASC');

        const { rows } = await Db.query(query);
        return rows;
    }

    /**
     * Check if event has VolleyStation enabled
     * @param {number} eventId - The event ID
     * @returns {Promise<boolean>} Whether VolleyStation is enabled
     */
    async isEventEnabledForEvent(eventId) {
        const query = squel
            .select()
            .from('event')
            .field('event_id')
            .where('event_id = ?', eventId)
            // .where('enable_volley_station = ?', true);

        const { rows } = await Db.query(query);
        return rows.length > 0;
    }
}

module.exports = new VolleyStationService();
